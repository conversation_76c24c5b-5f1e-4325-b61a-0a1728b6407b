import { useEffect, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { User } from "@supabase/supabase-js";
import AdminHeader from "./AdminHeader";
import AdminSidebar from "./AdminSidebar";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import tennisCourtCorner from "@/assets/tennis-court-corner.jpg";

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout = ({ children }: AdminLayoutProps) => {
  const { user, isLoading, isAdmin } = useAuth();
  const location = useLocation();
  
  // Check if we're in preview mode (for Lovable previews)
  const isPreview = window.location.hostname.includes('lovable') || 
                   window.location.hostname.includes('localhost') ||
                   window.location.hostname.includes('preview') ||
                   window.location.hostname === '127.0.0.1' ||
                   window.location.port === '5173';

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-tennis-green mx-auto mb-4"></div>
          <p className="text-muted-foreground">Authentifizierung wird geladen...</p>
        </div>
      </div>
    );
  }

  // Redirect if user is not logged in (skip in preview mode)
  if (!user && !isPreview) {
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  // Redirect if user is not admin (skip in preview mode)
  if (!isAdmin && !isPreview) {
    toast.error('Zugriff verweigert: Admin-Berechtigung erforderlich');
    return <Navigate to="/" replace />;
  }

  return (
    <div 
      className="flex h-screen bg-background relative"
      style={{
        backgroundImage: `url(${tennisCourtCorner})`,
        backgroundSize: 'cover',
        backgroundPosition: 'bottom right',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Overlay to make background more subtle */}
      <div className="absolute inset-0 bg-background/75 backdrop-blur-[1px]" />
      
      <div className="relative z-10 flex h-screen w-full">
        <AdminSidebar />
        <div className="flex-1 flex flex-col overflow-hidden">
          <AdminHeader />
          <main className="flex-1 overflow-y-auto">
            <div className="container mx-auto px-6 py-8">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;